import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { 
  Shield, 
  CheckCircle, 
  AlertCircle, 
  Loader2,
  User,
  Globe,
  LogOut,
  Mail
} from 'lucide-react';
import { useToast } from "@/hooks/use-toast";
import { useAuth } from '@/hooks/useAuth';
import EmailVerification from '@/components/EmailVerification';
import AuthForm from '@/components/AuthForm';
import { apiService } from '@/services/apiService';

interface AuthModuleProps {
  isAuthenticated: boolean;
  onAuthChange: (authenticated: boolean) => void;
}

const AuthModule = ({ isAuthenticated, onAuthChange }: AuthModuleProps) => {
  const [sessionStatus, setSessionStatus] = useState<'idle' | 'checking' | 'active' | 'expired'>('idle');
  const [showEmailVerification, setShowEmailVerification] = useState(false);
  const [pendingEmail, setPendingEmail] = useState<string>('');
  const { user, logout, isLoading } = useAuth();
  const { toast } = useToast();

  // Sync with parent component
  useEffect(() => {
    onAuthChange(!!user?.emailVerified);
    setSessionStatus(user?.emailVerified ? 'active' : 'idle');
  }, [user, onAuthChange]);

  // Check for LinkedIn OAuth callback
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    const state = urlParams.get('state');
    
    if (code && state) {
      handleLinkedInCallback(code, state);
      // Clean up URL
      window.history.replaceState({}, document.title, window.location.pathname);
    }
  }, []);

  const handleLinkedInCallback = async (code: string, state: string) => {
    try {
      // This would normally be handled by the auth service
      // For demo purposes, we'll simulate requiring email verification
      setPendingEmail('<EMAIL>'); // This would come from LinkedIn
      setShowEmailVerification(true);
      
      toast({
        title: "LinkedIn Connected",
        description: "Please verify your email to complete registration",
      });
    } catch (error) {
      toast({
        title: "Authentication Failed",
        description: "Please try again",
        variant: "destructive"
      });
    }
  };

  const handleEmailVerificationRequired = (email: string) => {
    setPendingEmail(email);
    setShowEmailVerification(true);
  };

  const handleEmailVerificationComplete = () => {
    setShowEmailVerification(false);
    setPendingEmail('');
    toast({
      title: "Welcome!",
      description: "Your account is now verified and active"
    });
  };

  const handleSkipVerification = () => {
    setShowEmailVerification(false);
    setPendingEmail('');
    toast({
      title: "Verification Skipped",
      description: "You can verify your email later from your account settings",
      variant: "default"
    });
  };

  const handleLogout = async () => {
    await logout();
    setSessionStatus('idle');
    toast({
      title: "Logged Out",
      description: "Successfully logged out"
    });
  };

  const checkSession = async () => {
    setSessionStatus('checking');
    await new Promise(resolve => setTimeout(resolve, 1000));
    setSessionStatus(user?.emailVerified ? 'active' : 'expired');
  };

  // Show email verification screen if needed
  if (showEmailVerification && pendingEmail) {
    return (
      <EmailVerification
        email={pendingEmail}
        onVerificationComplete={handleEmailVerificationComplete}
        onSkipVerification={handleSkipVerification}
      />
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
      </div>
    );
  }

  return (
    <div className="grid gap-6 md:grid-cols-2">
      {/* Authentication Form */}
      <div>
        {!user || !user.emailVerified ? (
          <AuthForm onVerificationRequired={handleEmailVerificationRequired} />
        ) : (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="w-5 h-5" />
                Account
              </CardTitle>
              <CardDescription>
                Your account is authenticated and verified
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  Successfully authenticated
                </AlertDescription>
              </Alert>
              
              <div className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                {user.profilePicture ? (
                  <img 
                    src={user.profilePicture} 
                    alt={user.name}
                    className="w-10 h-10 rounded-full"
                  />
                ) : (
                  <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                    <User className="w-5 h-5 text-white" />
                  </div>
                )}
                <div className="flex-1">
                  <p className="font-medium text-sm">{user.name}</p>
                  <p className="text-xs text-gray-600 dark:text-gray-400">{user.email}</p>
                  <p className="text-xs text-gray-500 dark:text-gray-500 capitalize">
                    {user.loginMethod} account
                  </p>
                </div>
                {user.emailVerified && (
                  <CheckCircle className="w-4 h-4 text-green-500" />
                )}
              </div>
              
              <Button onClick={handleLogout} variant="outline" className="w-full">
                <LogOut className="w-4 h-4 mr-2" />
                Sign Out
              </Button>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Session Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="w-5 h-5" />
            Session Status
          </CardTitle>
          <CardDescription>
            Monitor your LinkedIn session and connection health
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <span>Connection Status:</span>
            <Badge variant={user?.emailVerified ? "default" : "secondary"}>
              {user?.emailVerified ? "Active" : "Inactive"}
            </Badge>
          </div>
          
          <div className="flex items-center justify-between">
            <span>Email Status:</span>
            <div className="flex items-center gap-2">
              {user?.emailVerified ? (
                <>
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <Badge variant="default" className="bg-green-100 text-green-800 border-green-200 dark:bg-green-900 dark:text-green-200">
                    Verified
                  </Badge>
                </>
              ) : (
                <>
                  <Mail className="w-4 h-4 text-gray-500" />
                  <Badge variant="secondary">
                    Not Verified
                  </Badge>
                </>
              )}
            </div>
          </div>

          <div className="flex items-center justify-between">
            <span>Session Health:</span>
            <div className="flex items-center gap-2">
              {sessionStatus === 'checking' && <Loader2 className="w-4 h-4 animate-spin" />}
              {sessionStatus === 'active' && <CheckCircle className="w-4 h-4 text-green-500" />}
              {sessionStatus === 'expired' && <AlertCircle className="w-4 h-4 text-red-500" />}
              <Badge variant={
                sessionStatus === 'active' ? 'default' : 
                sessionStatus === 'expired' ? 'destructive' : 'secondary'
              }>
                {sessionStatus.charAt(0).toUpperCase() + sessionStatus.slice(1)}
              </Badge>
            </div>
          </div>

          <Separator />

          <Button 
            onClick={checkSession} 
            variant="outline" 
            className="w-full"
            disabled={sessionStatus === 'checking'}
          >
            {sessionStatus === 'checking' ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Checking...
              </>
            ) : (
              'Check Session Status'
            )}
          </Button>

          {user?.emailVerified && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                Your session is active and ready for job applications
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default AuthModule;
