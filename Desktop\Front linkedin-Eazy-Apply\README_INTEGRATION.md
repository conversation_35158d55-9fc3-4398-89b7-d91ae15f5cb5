# LinkedIn EasyApply - Full Stack Integration

This project combines a React TypeScript frontend with a Python Selenium backend through a FastAPI REST API, enabling automated LinkedIn job applications with real-time monitoring.

## 🏗️ Architecture Overview

```
┌─────────────────┐    HTTP/WebSocket    ┌─────────────────┐    Python API    ┌─────────────────┐
│   React Frontend │ ◄─────────────────► │   FastAPI Server │ ◄──────────────► │ Selenium Backend │
│   (Port 5173)   │                     │   (Port 8000)    │                  │  (LinkedIn Bot)  │
└─────────────────┘                     └─────────────────┘                  └─────────────────┘
```

### Components:
- **Frontend**: React 18 + TypeScript + Vite + Tailwind CSS
- **API Layer**: FastAPI + SQLAlchemy + WebSocket
- **Backend**: Python + Selenium WebDriver + Chrome automation
- **Database**: SQLite (development) / PostgreSQL (production)
- **Real-time**: WebSocket for live job application updates

## 🚀 Quick Start

### 1. Backend Setup

```bash
cd "Backend EasyApply"

# Install Python dependencies
pip install -r requirements.txt

# Configure LinkedIn credentials
# Edit config.py with your LinkedIn email and password

# Start the API server
python start_server.py
```

The API server will be available at:
- **API**: http://localhost:8000
- **Documentation**: http://localhost:8000/docs
- **WebSocket**: ws://localhost:8000/ws/jobs/{user_id}

### 2. Frontend Setup

```bash
# Install Node.js dependencies
npm install

# Configure environment variables
cp .env.example .env
# Edit .env if needed (default values should work for local development)

# Start the development server
npm run dev
```

The frontend will be available at: http://localhost:5173

## 📋 Features

### ✅ Implemented Features

#### Authentication System
- User registration and login with JWT tokens
- Secure password hashing with bcrypt
- Session management and token validation

#### Job Search Automation
- Configurable search parameters (keywords, location, experience level)
- Real-time progress monitoring via WebSocket
- Background job processing with FastAPI
- Start/stop job search control

#### Application Tracking
- Complete job application history
- Application statistics and success rates
- Export functionality (CSV)
- Real-time status updates

#### API Integration
- RESTful API with comprehensive endpoints
- WebSocket for real-time communication
- Database persistence with SQLAlchemy
- Error handling and logging

### 🔄 API Endpoints

#### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/auth/me` - Get current user

#### Job Search
- `POST /api/jobs/search/start` - Start job search automation
- `GET /api/jobs/search/status/{session_id}` - Get search status
- `POST /api/jobs/search/stop/{session_id}` - Stop job search

#### Applications
- `GET /api/jobs/applications` - Get job applications
- `GET /api/jobs/applications/stats` - Get application statistics
- `GET /api/jobs/sessions` - Get job search sessions

#### WebSocket
- `WS /ws/jobs/{user_id}` - Real-time job search updates

## 🛠️ Configuration

### Backend Configuration (`Backend EasyApply/config.py`)

```python
# LinkedIn Credentials
email = "<EMAIL>"
password = "your-linkedin-password"

# Job Search Parameters
keywords = ["web development", "frontend developer", "react developer"]
location = ["Europe"]
experienceLevels = ["Entry level"]
jobType = ["Full-time", "Part-time", "Contract"]
remote = ["On-site", "Remote", "Hybrid"]
datePosted = ["Past 24 hours"]
salary = ["$80,000+"]
```

### Frontend Configuration (`.env`)

```bash
VITE_API_BASE_URL=http://localhost:8000
VITE_WS_BASE_URL=ws://localhost:8000
```

## 🔧 Development

### Project Structure

```
├── src/                          # Frontend source code
│   ├── components/
│   │   └── modules/
│   │       ├── JobSearchModuleAPI.tsx    # API-integrated job search
│   │       └── ApplicationTrackerAPI.tsx # API-integrated app tracker
│   ├── services/
│   │   └── apiService.ts         # API client and WebSocket manager
│   └── pages/
│       └── Index.tsx             # Main application page
├── Backend EasyApply/
│   ├── api_server.py             # FastAPI application
│   ├── linkedin.py               # Selenium automation
│   ├── config.py                 # Configuration
│   ├── start_server.py           # Server startup script
│   └── requirements.txt          # Python dependencies
└── README_INTEGRATION.md         # This file
```

### Key Files

#### `src/services/apiService.ts`
- HTTP client with axios
- JWT token management
- WebSocket connection manager
- TypeScript interfaces for API responses

#### `Backend EasyApply/api_server.py`
- FastAPI application with CORS
- SQLAlchemy database models
- JWT authentication
- WebSocket connection management
- Background task processing

#### `Backend EasyApply/linkedin.py`
- Selenium WebDriver automation
- LinkedIn job search and application logic
- Cookie management and session persistence

## 🧪 Testing

### Backend Testing
```bash
cd "Backend EasyApply"

# Test API endpoints
curl http://localhost:8000/api/health

# View API documentation
open http://localhost:8000/docs
```

### Frontend Testing
```bash
# Run development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

## 🚀 Deployment

### Backend Deployment
1. Set up production database (PostgreSQL recommended)
2. Configure environment variables
3. Use a production WSGI server (uvicorn, gunicorn)
4. Set up reverse proxy (nginx)

### Frontend Deployment
1. Build the application: `npm run build`
2. Deploy the `dist` folder to a static hosting service
3. Configure environment variables for production API URL

## 🔒 Security Considerations

- Store LinkedIn credentials securely
- Use environment variables for sensitive configuration
- Implement rate limiting for API endpoints
- Use HTTPS in production
- Validate and sanitize all user inputs

## 🐛 Troubleshooting

### Common Issues

1. **CORS Errors**: Ensure the API server is running and CORS is properly configured
2. **WebSocket Connection Failed**: Check that the WebSocket URL is correct and the server is running
3. **LinkedIn Login Issues**: Verify credentials in config.py and check for 2FA requirements
4. **Chrome Driver Issues**: Ensure Chrome and ChromeDriver are properly installed

### Logs and Debugging

- Backend logs: Check console output from `python start_server.py`
- Frontend logs: Check browser developer console
- API documentation: Visit http://localhost:8000/docs for interactive API testing

## 📝 Next Steps

1. **Enhanced Error Handling**: Implement comprehensive error handling and user feedback
2. **Job Filtering**: Add advanced job filtering and matching algorithms
3. **Analytics Dashboard**: Create detailed analytics and reporting features
4. **Mobile Responsiveness**: Optimize the UI for mobile devices
5. **Testing Suite**: Add comprehensive unit and integration tests
6. **Docker Support**: Create Docker containers for easy deployment

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is for educational purposes. Please ensure compliance with LinkedIn's Terms of Service when using automation tools.
