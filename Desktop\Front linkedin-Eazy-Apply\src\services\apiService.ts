/**
 * API Service for LinkedIn EasyApply Frontend
 * Handles all communication with the backend API
 */

import axios, { AxiosInstance, AxiosResponse } from 'axios';

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';
const WS_BASE_URL = import.meta.env.VITE_WS_BASE_URL || 'ws://localhost:8000';

// Types
export interface User {
  id: number;
  email: string;
  full_name: string;
  is_active: boolean;
  created_at: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  full_name: string;
}

export interface AuthResponse {
  access_token: string;
  token_type: string;
}

export interface JobSearchRequest {
  keywords: string[];
  locations: string[];
  experience_levels: string[];
  job_types: string[];
  remote_options: string[];
  date_posted: string[];
  salary_range?: string[];
  max_applications?: number;
}

export interface JobApplication {
  id: number;
  job_title: string;
  company_name: string;
  job_location: string;
  application_status: string;
  applied_at: string;
  job_url: string;
}

export interface SessionStatus {
  session_id: number;
  status: string;
  total_jobs_found: number;
  jobs_applied: number;
  jobs_skipped: number;
  started_at: string;
  completed_at?: string;
}

export interface ApplicationStats {
  total_applications: number;
  successful_applications: number;
  failed_applications: number;
  skipped_applications: number;
  success_rate: number;
}

export interface JobSession {
  id: number;
  status: string;
  total_jobs_found: number;
  jobs_applied: number;
  jobs_skipped: number;
  started_at: string;
  completed_at?: string;
  search_parameters: JobSearchRequest;
}

class ApiService {
  private api: AxiosInstance;
  private token: string | null = null;

  constructor() {
    this.api = axios.create({
      baseURL: API_BASE_URL,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Load token from localStorage
    this.token = localStorage.getItem('access_token');
    if (this.token) {
      this.setAuthHeader(this.token);
    }

    // Request interceptor to add auth header
    this.api.interceptors.request.use(
      (config) => {
        if (this.token) {
          config.headers.Authorization = `Bearer ${this.token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor to handle auth errors
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          this.logout();
          window.location.href = '/';
        }
        return Promise.reject(error);
      }
    );
  }

  private setAuthHeader(token: string) {
    this.token = token;
    this.api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    localStorage.setItem('access_token', token);
  }

  // Authentication Methods
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    const response: AxiosResponse<AuthResponse> = await this.api.post('/api/auth/login', credentials);
    this.setAuthHeader(response.data.access_token);
    return response.data;
  }

  async register(userData: RegisterRequest): Promise<AuthResponse> {
    const response: AxiosResponse<AuthResponse> = await this.api.post('/api/auth/register', userData);
    this.setAuthHeader(response.data.access_token);
    return response.data;
  }

  async getCurrentUser(): Promise<User> {
    const response: AxiosResponse<User> = await this.api.get('/api/auth/me');
    return response.data;
  }

  logout() {
    this.token = null;
    delete this.api.defaults.headers.common['Authorization'];
    localStorage.removeItem('access_token');
  }

  // Job Search Methods
  async startJobSearch(searchRequest: JobSearchRequest): Promise<{ session_id: number; status: string; message: string }> {
    const response = await this.api.post('/api/jobs/search/start', searchRequest);
    return response.data;
  }

  async getSearchStatus(sessionId: number): Promise<SessionStatus> {
    const response: AxiosResponse<SessionStatus> = await this.api.get(`/api/jobs/search/status/${sessionId}`);
    return response.data;
  }

  async stopJobSearch(sessionId: number): Promise<{ message: string }> {
    const response = await this.api.post(`/api/jobs/search/stop/${sessionId}`);
    return response.data;
  }

  // Job Applications Methods
  async getJobApplications(skip: number = 0, limit: number = 100): Promise<JobApplication[]> {
    const response: AxiosResponse<JobApplication[]> = await this.api.get('/api/jobs/applications', {
      params: { skip, limit }
    });
    return response.data;
  }

  async getApplicationStats(): Promise<ApplicationStats> {
    const response: AxiosResponse<ApplicationStats> = await this.api.get('/api/jobs/applications/stats');
    return response.data;
  }

  async getJobSessions(): Promise<JobSession[]> {
    const response: AxiosResponse<JobSession[]> = await this.api.get('/api/jobs/sessions');
    return response.data;
  }

  // Health Check
  async healthCheck(): Promise<{ status: string; timestamp: string }> {
    const response = await this.api.get('/api/health');
    return response.data;
  }

  // WebSocket Connection
  createWebSocketConnection(userId: number): WebSocket {
    const wsUrl = `${WS_BASE_URL}/ws/jobs/${userId}`;
    return new WebSocket(wsUrl);
  }

  // Utility Methods
  isAuthenticated(): boolean {
    return !!this.token;
  }

  getToken(): string | null {
    return this.token;
  }
}

// WebSocket Manager Class
export class WebSocketManager {
  private ws: WebSocket | null = null;
  private userId: number | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  constructor(private onMessage: (data: any) => void, private onError?: (error: Event) => void) {}

  connect(userId: number) {
    this.userId = userId;
    this.ws = apiService.createWebSocketConnection(userId);

    this.ws.onopen = () => {
      console.log('WebSocket connected');
      this.reconnectAttempts = 0;
    };

    this.ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        this.onMessage(data);
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    };

    this.ws.onclose = () => {
      console.log('WebSocket disconnected');
      this.attemptReconnect();
    };

    this.ws.onerror = (error) => {
      console.error('WebSocket error:', error);
      if (this.onError) {
        this.onError(error);
      }
    };
  }

  private attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts && this.userId) {
      this.reconnectAttempts++;
      console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      
      setTimeout(() => {
        this.connect(this.userId!);
      }, this.reconnectDelay * this.reconnectAttempts);
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }

  send(message: any) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    }
  }
}

// Export singleton instance
export const apiService = new ApiService();
export default apiService;
