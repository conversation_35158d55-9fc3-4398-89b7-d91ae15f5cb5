import { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { 
  Search, 
  Play, 
  Pause, 
  Square, 
  Settings, 
  MapPin, 
  Briefcase, 
  Clock, 
  DollarSign,
  Target,
  Filter,
  AlertCircle,
  CheckCircle,
  Loader2,
  Zap,
  Activity
} from 'lucide-react';
import { useToast } from "@/hooks/use-toast";
import { apiService, WebSocketManager, JobSearchRequest, SessionStatus } from '@/services/apiService';
import { useAuth } from '@/hooks/useAuth';

interface JobSearchModuleAPIProps {
  isAuthenticated: boolean;
}

const JobSearchModuleAPI = ({ isAuthenticated }: JobSearchModuleAPIProps) => {
  // Search Configuration State
  const [searchConfig, setSearchConfig] = useState<JobSearchRequest>({
    keywords: ['web development', 'frontend developer', 'react developer'],
    locations: ['Europe'],
    experience_levels: ['Entry level'],
    job_types: ['Full-time', 'Part-time', 'Contract'],
    remote_options: ['On-site', 'Remote', 'Hybrid'],
    date_posted: ['Past 24 hours'],
    salary_range: ['$80,000+'],
    max_applications: 50
  });

  // Session State
  const [currentSession, setCurrentSession] = useState<SessionStatus | null>(null);
  const [isSearching, setIsSearching] = useState(false);
  const [searchLogs, setSearchLogs] = useState<string[]>([]);
  const [progress, setProgress] = useState(0);

  // WebSocket
  const wsManager = useRef<WebSocketManager | null>(null);
  const { user } = useAuth();
  const { toast } = useToast();

  // Initialize WebSocket connection
  useEffect(() => {
    if (user && isAuthenticated) {
      wsManager.current = new WebSocketManager(
        (data) => handleWebSocketMessage(data),
        (error) => console.error('WebSocket error:', error)
      );
      wsManager.current.connect(user.id);
    }

    return () => {
      if (wsManager.current) {
        wsManager.current.disconnect();
      }
    };
  }, [user, isAuthenticated]);

  const handleWebSocketMessage = (data: any) => {
    console.log('WebSocket message:', data);
    
    switch (data.type) {
      case 'progress':
        setSearchLogs(prev => [...prev, data.message]);
        break;
      case 'job_update':
        setSearchLogs(prev => [...prev, data.message]);
        if (data.message.includes('Just Applied')) {
          setProgress(prev => Math.min(prev + 2, 100));
        }
        break;
      case 'completed':
        setIsSearching(false);
        setProgress(100);
        setSearchLogs(prev => [...prev, data.message]);
        toast({
          title: "Job Search Completed",
          description: data.message,
        });
        break;
      case 'error':
        setIsSearching(false);
        setSearchLogs(prev => [...prev, `Error: ${data.message}`]);
        toast({
          title: "Search Error",
          description: data.message,
          variant: "destructive"
        });
        break;
    }
  };

  const startJobSearch = async () => {
    if (!isAuthenticated) {
      toast({
        title: "Authentication Required",
        description: "Please authenticate first to start job search",
        variant: "destructive"
      });
      return;
    }

    try {
      setIsSearching(true);
      setSearchLogs([]);
      setProgress(0);
      
      const response = await apiService.startJobSearch(searchConfig);
      
      setCurrentSession({
        session_id: response.session_id,
        status: 'running',
        total_jobs_found: 0,
        jobs_applied: 0,
        jobs_skipped: 0,
        started_at: new Date().toISOString()
      });

      toast({
        title: "Job Search Started",
        description: response.message,
      });

      // Start polling for session status
      pollSessionStatus(response.session_id);

    } catch (error: any) {
      setIsSearching(false);
      toast({
        title: "Failed to Start Search",
        description: error.response?.data?.detail || "An error occurred",
        variant: "destructive"
      });
    }
  };

  const stopJobSearch = async () => {
    if (!currentSession) return;

    try {
      await apiService.stopJobSearch(currentSession.session_id);
      setIsSearching(false);
      toast({
        title: "Job Search Stopped",
        description: "The job search has been stopped successfully",
      });
    } catch (error: any) {
      toast({
        title: "Failed to Stop Search",
        description: error.response?.data?.detail || "An error occurred",
        variant: "destructive"
      });
    }
  };

  const pollSessionStatus = async (sessionId: number) => {
    const interval = setInterval(async () => {
      try {
        const status = await apiService.getSearchStatus(sessionId);
        setCurrentSession(status);
        
        if (status.status === 'completed' || status.status === 'failed' || status.status === 'stopped') {
          clearInterval(interval);
          setIsSearching(false);
        }
      } catch (error) {
        clearInterval(interval);
        setIsSearching(false);
      }
    }, 2000);

    // Clean up interval after 30 minutes
    setTimeout(() => clearInterval(interval), 30 * 60 * 1000);
  };

  const updateSearchConfig = (field: keyof JobSearchRequest, value: any) => {
    setSearchConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const addKeyword = (keyword: string) => {
    if (keyword && !searchConfig.keywords.includes(keyword)) {
      updateSearchConfig('keywords', [...searchConfig.keywords, keyword]);
    }
  };

  const removeKeyword = (keyword: string) => {
    updateSearchConfig('keywords', searchConfig.keywords.filter(k => k !== keyword));
  };

  if (!isAuthenticated) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="w-5 h-5" />
            Job Search & Auto Apply
          </CardTitle>
          <CardDescription>
            Automated LinkedIn job search and application system
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Please authenticate with your LinkedIn account to access job search features.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Search Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            Search Configuration
          </CardTitle>
          <CardDescription>
            Configure your job search parameters
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Keywords */}
          <div className="space-y-2">
            <Label>Job Keywords</Label>
            <div className="flex flex-wrap gap-2 mb-2">
              {searchConfig.keywords.map((keyword, index) => (
                <Badge key={index} variant="secondary" className="cursor-pointer" onClick={() => removeKeyword(keyword)}>
                  {keyword} ×
                </Badge>
              ))}
            </div>
            <div className="flex gap-2">
              <Input 
                placeholder="Add keyword..." 
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    addKeyword((e.target as HTMLInputElement).value);
                    (e.target as HTMLInputElement).value = '';
                  }
                }}
              />
            </div>
          </div>

          {/* Locations */}
          <div className="space-y-2">
            <Label>Locations</Label>
            <Select value={searchConfig.locations[0]} onValueChange={(value) => updateSearchConfig('locations', [value])}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Europe">Europe</SelectItem>
                <SelectItem value="United States">United States</SelectItem>
                <SelectItem value="Canada">Canada</SelectItem>
                <SelectItem value="United Kingdom">United Kingdom</SelectItem>
                <SelectItem value="Germany">Germany</SelectItem>
                <SelectItem value="France">France</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Experience Level */}
          <div className="space-y-2">
            <Label>Experience Level</Label>
            <Select value={searchConfig.experience_levels[0]} onValueChange={(value) => updateSearchConfig('experience_levels', [value])}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Internship">Internship</SelectItem>
                <SelectItem value="Entry level">Entry level</SelectItem>
                <SelectItem value="Associate">Associate</SelectItem>
                <SelectItem value="Mid-Senior level">Mid-Senior level</SelectItem>
                <SelectItem value="Director">Director</SelectItem>
                <SelectItem value="Executive">Executive</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Max Applications */}
          <div className="space-y-2">
            <Label>Maximum Applications</Label>
            <Input 
              type="number" 
              value={searchConfig.max_applications} 
              onChange={(e) => updateSearchConfig('max_applications', parseInt(e.target.value))}
              min="1"
              max="200"
            />
          </div>
        </CardContent>
      </Card>

      {/* Search Control */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="w-5 h-5" />
            Job Search Control
          </CardTitle>
          <CardDescription>
            Start, monitor, and control your automated job search
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button 
              onClick={startJobSearch} 
              disabled={isSearching}
              className="flex items-center gap-2"
            >
              {isSearching ? <Loader2 className="w-4 h-4 animate-spin" /> : <Play className="w-4 h-4" />}
              {isSearching ? 'Searching...' : 'Start Job Search'}
            </Button>
            
            {isSearching && (
              <Button 
                onClick={stopJobSearch} 
                variant="destructive"
                className="flex items-center gap-2"
              >
                <Square className="w-4 h-4" />
                Stop Search
              </Button>
            )}
          </div>

          {/* Session Status */}
          {currentSession && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Session Status:</span>
                <Badge variant={currentSession.status === 'running' ? 'default' : 'secondary'}>
                  {currentSession.status}
                </Badge>
              </div>
              
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">Jobs Found:</span>
                  <div className="font-medium">{currentSession.total_jobs_found}</div>
                </div>
                <div>
                  <span className="text-muted-foreground">Applied:</span>
                  <div className="font-medium text-green-600">{currentSession.jobs_applied}</div>
                </div>
                <div>
                  <span className="text-muted-foreground">Skipped:</span>
                  <div className="font-medium text-yellow-600">{currentSession.jobs_skipped}</div>
                </div>
              </div>

              {isSearching && (
                <Progress value={progress} className="w-full" />
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Live Logs */}
      {searchLogs.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="w-5 h-5" />
              Live Activity Log
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-black text-green-400 p-4 rounded-lg font-mono text-sm max-h-60 overflow-y-auto">
              {searchLogs.map((log, index) => (
                <div key={index} className="mb-1">
                  {log}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default JobSearchModuleAPI;
