
import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { 
  Search, 
  Settings, 
  BarChart3, 
  FileText, 
  Monitor, 
  Shield,
  Briefcase,
  TrendingUp,
  Zap,
  Activity
} from 'lucide-react';
import AuthModule from '@/components/modules/AuthModule';
import JobSearchModuleAPI from '@/components/modules/JobSearchModuleAPI';
import ApplicationTrackerAPI from '@/components/modules/ApplicationTrackerAPI';
import ConfigurationModule from '@/components/modules/ConfigurationModule';
import AnalyticsModule from '@/components/modules/AnalyticsModule';
import MonitoringModule from '@/components/modules/MonitoringModule';
import DarkModeToggle from '@/components/DarkModeToggle';

const Index = () => {
  const [activeTab, setActiveTab] = useState('auth');
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 transition-colors duration-300">
      {/* Modern Header */}
      <div className="bg-white/80 backdrop-blur-lg border-b shadow-sm sticky top-0 z-50 dark:bg-gray-900/80 dark:border-gray-700">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl">
                <Zap className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 dark:from-gray-100 dark:to-gray-300 bg-clip-text text-transparent">
                  LinkedIn AutoApply
                </h1>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Intelligent job application automation
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <DarkModeToggle />
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${isAuthenticated ? 'bg-green-500 animate-pulse' : 'bg-gray-300 dark:bg-gray-600'}`}></div>
                <Badge 
                  variant={isAuthenticated ? "default" : "secondary"} 
                  className={`px-3 py-1 ${isAuthenticated ? 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900 dark:text-green-200 dark:border-green-800' : ''}`}
                >
                  {isAuthenticated ? "Connected" : "Disconnected"}
                </Badge>
              </div>
              <Button size="sm" variant="outline" className="hidden md:flex">
                <Activity className="w-4 h-4 mr-2" />
                Status
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-6 py-8">
        {/* Modern Dashboard */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <div className="mb-8">
            <TabsList className="grid w-full grid-cols-6 h-14 bg-white/70 backdrop-blur-sm p-1 shadow-lg rounded-2xl border-0 dark:bg-gray-800/70">
              <TabsTrigger 
                value="auth" 
                className="flex flex-col items-center gap-1 py-3 rounded-xl data-[state=active]:bg-white data-[state=active]:shadow-md dark:data-[state=active]:bg-gray-700 transition-all duration-200"
              >
                <Shield className="w-4 h-4" />
                <span className="text-xs font-medium hidden sm:block">Auth</span>
              </TabsTrigger>
              <TabsTrigger 
                value="search" 
                className="flex flex-col items-center gap-1 py-3 rounded-xl data-[state=active]:bg-white data-[state=active]:shadow-md dark:data-[state=active]:bg-gray-700 transition-all duration-200"
              >
                <Search className="w-4 h-4" />
                <span className="text-xs font-medium hidden sm:block">Search</span>
              </TabsTrigger>
              <TabsTrigger 
                value="applications" 
                className="flex flex-col items-center gap-1 py-3 rounded-xl data-[state=active]:bg-white data-[state=active]:shadow-md dark:data-[state=active]:bg-gray-700 transition-all duration-200"
              >
                <Briefcase className="w-4 h-4" />
                <span className="text-xs font-medium hidden sm:block">Apps</span>
              </TabsTrigger>
              <TabsTrigger 
                value="analytics" 
                className="flex flex-col items-center gap-1 py-3 rounded-xl data-[state=active]:bg-white data-[state=active]:shadow-md dark:data-[state=active]:bg-gray-700 transition-all duration-200"
              >
                <BarChart3 className="w-4 h-4" />
                <span className="text-xs font-medium hidden sm:block">Analytics</span>
              </TabsTrigger>
              <TabsTrigger 
                value="config" 
                className="flex flex-col items-center gap-1 py-3 rounded-xl data-[state=active]:bg-white data-[state=active]:shadow-md dark:data-[state=active]:bg-gray-700 transition-all duration-200"
              >
                <Settings className="w-4 h-4" />
                <span className="text-xs font-medium hidden sm:block">Config</span>
              </TabsTrigger>
              <TabsTrigger 
                value="monitoring" 
                className="flex flex-col items-center gap-1 py-3 rounded-xl data-[state=active]:bg-white data-[state=active]:shadow-md dark:data-[state=active]:bg-gray-700 transition-all duration-200"
              >
                <Monitor className="w-4 h-4" />
                <span className="text-xs font-medium hidden sm:block">Monitor</span>
              </TabsTrigger>
            </TabsList>
          </div>

          <div className="bg-white/40 backdrop-blur-sm rounded-3xl p-6 shadow-xl border border-white/20 dark:bg-gray-800/40 dark:border-gray-700/20">
            <TabsContent value="auth" className="mt-0">
              <AuthModule 
                isAuthenticated={isAuthenticated}
                onAuthChange={setIsAuthenticated}
              />
            </TabsContent>

            <TabsContent value="search" className="mt-0">
              <JobSearchModuleAPI isAuthenticated={isAuthenticated} />
            </TabsContent>

            <TabsContent value="applications" className="mt-0">
              <ApplicationTrackerAPI isAuthenticated={isAuthenticated} />
            </TabsContent>

            <TabsContent value="analytics" className="mt-0">
              <AnalyticsModule isAuthenticated={isAuthenticated} />
            </TabsContent>

            <TabsContent value="config" className="mt-0">
              <ConfigurationModule isAuthenticated={isAuthenticated} />
            </TabsContent>

            <TabsContent value="monitoring" className="mt-0">
              <MonitoringModule isAuthenticated={isAuthenticated} />
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </div>
  );
};

export default Index;
