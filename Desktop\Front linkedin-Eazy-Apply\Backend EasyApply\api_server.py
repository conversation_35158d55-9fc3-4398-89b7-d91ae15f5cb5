"""
FastAPI server for LinkedIn EasyApply automation
Provides REST API endpoints to control the LinkedIn bot from the frontend
"""

import os
import asyncio
import json
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from fastapi import FastAPI, HTTPException, Depends, WebSocket, WebSocketDisconnect, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from pydantic import BaseModel, EmailStr
from sqlalchemy import create_engine, Column, Integer, String, DateTime, Boolean, Text, Float
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from passlib.context import CryptContext
from jose import JWTError, jwt
import threading
import queue
import time

# Import existing LinkedIn automation
from linkedin import Linkedin
import config
import utils

# Database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///./linkedin_easyapply.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# Security
SECRET_KEY = "your-secret-key-change-this-in-production"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
security = HTTPBearer()

# FastAPI app
app = FastAPI(title="LinkedIn EasyApply API", version="1.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://localhost:3000"],  # Frontend URLs
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Database Models
class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True)
    hashed_password = Column(String)
    full_name = Column(String)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)

class JobApplication(Base):
    __tablename__ = "job_applications"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, index=True)
    job_id = Column(String, index=True)
    job_title = Column(String)
    company_name = Column(String)
    job_location = Column(String)
    job_url = Column(String)
    application_status = Column(String)  # applied, failed, skipped
    applied_at = Column(DateTime, default=datetime.utcnow)
    job_description = Column(Text, nullable=True)

class JobSearchSession(Base):
    __tablename__ = "job_search_sessions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, index=True)
    session_status = Column(String)  # running, completed, failed, stopped
    total_jobs_found = Column(Integer, default=0)
    jobs_applied = Column(Integer, default=0)
    jobs_skipped = Column(Integer, default=0)
    started_at = Column(DateTime, default=datetime.utcnow)
    completed_at = Column(DateTime, nullable=True)
    search_parameters = Column(Text)  # JSON string of search params

# Create tables
Base.metadata.create_all(bind=engine)

# Pydantic models
class UserCreate(BaseModel):
    email: EmailStr
    password: str
    full_name: str

class UserLogin(BaseModel):
    email: EmailStr
    password: str

class Token(BaseModel):
    access_token: str
    token_type: str

class JobSearchRequest(BaseModel):
    keywords: List[str]
    locations: List[str]
    experience_levels: List[str]
    job_types: List[str]
    remote_options: List[str]
    date_posted: List[str]
    salary_range: Optional[List[str]] = None
    max_applications: Optional[int] = 50

class JobApplicationResponse(BaseModel):
    id: int
    job_title: str
    company_name: str
    job_location: str
    application_status: str
    applied_at: datetime
    job_url: str

class SessionStatus(BaseModel):
    session_id: int
    status: str
    total_jobs_found: int
    jobs_applied: int
    jobs_skipped: int
    started_at: datetime
    completed_at: Optional[datetime]

# WebSocket connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

    async def broadcast(self, message: str):
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except:
                # Remove disconnected connections
                self.active_connections.remove(connection)

manager = ConnectionManager()

# Global variables for job automation
automation_queue = queue.Queue()
automation_thread = None
current_session = None

# Utility functions
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password):
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security), db: Session = Depends(get_db)):
    credentials_exception = HTTPException(
        status_code=401,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(credentials.credentials, SECRET_KEY, algorithms=[ALGORITHM])
        email: str = payload.get("sub")
        if email is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
    
    user = db.query(User).filter(User.email == email).first()
    if user is None:
        raise credentials_exception
    return user

# API Endpoints
@app.post("/api/auth/register", response_model=Token)
async def register(user: UserCreate, db: Session = Depends(get_db)):
    # Check if user already exists
    db_user = db.query(User).filter(User.email == user.email).first()
    if db_user:
        raise HTTPException(status_code=400, detail="Email already registered")
    
    # Create new user
    hashed_password = get_password_hash(user.password)
    db_user = User(
        email=user.email,
        hashed_password=hashed_password,
        full_name=user.full_name
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    
    # Create access token
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.email}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}

@app.post("/api/auth/login", response_model=Token)
async def login(user: UserLogin, db: Session = Depends(get_db)):
    db_user = db.query(User).filter(User.email == user.email).first()
    if not db_user or not verify_password(user.password, db_user.hashed_password):
        raise HTTPException(status_code=401, detail="Incorrect email or password")
    
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.email}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}

@app.get("/api/auth/me")
async def get_current_user_info(current_user: User = Depends(get_current_user)):
    return {
        "id": current_user.id,
        "email": current_user.email,
        "full_name": current_user.full_name,
        "is_active": current_user.is_active,
        "created_at": current_user.created_at
    }

# Job Search and Application Endpoints
@app.post("/api/jobs/search/start")
async def start_job_search(
    search_request: JobSearchRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Start a new job search and application session"""
    global current_session

    # Check if there's already a running session
    running_session = db.query(JobSearchSession).filter(
        JobSearchSession.user_id == current_user.id,
        JobSearchSession.session_status == "running"
    ).first()

    if running_session:
        raise HTTPException(status_code=400, detail="A job search session is already running")

    # Create new session
    session = JobSearchSession(
        user_id=current_user.id,
        session_status="running",
        search_parameters=json.dumps(search_request.dict())
    )
    db.add(session)
    db.commit()
    db.refresh(session)

    current_session = session

    # Start background job search
    background_tasks.add_task(run_job_automation, session.id, search_request, current_user.id)

    return {"session_id": session.id, "status": "started", "message": "Job search session started"}

@app.get("/api/jobs/search/status/{session_id}", response_model=SessionStatus)
async def get_search_status(
    session_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get the status of a job search session"""
    session = db.query(JobSearchSession).filter(
        JobSearchSession.id == session_id,
        JobSearchSession.user_id == current_user.id
    ).first()

    if not session:
        raise HTTPException(status_code=404, detail="Session not found")

    return SessionStatus(
        session_id=session.id,
        status=session.session_status,
        total_jobs_found=session.total_jobs_found,
        jobs_applied=session.jobs_applied,
        jobs_skipped=session.jobs_skipped,
        started_at=session.started_at,
        completed_at=session.completed_at
    )

@app.post("/api/jobs/search/stop/{session_id}")
async def stop_job_search(
    session_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Stop a running job search session"""
    session = db.query(JobSearchSession).filter(
        JobSearchSession.id == session_id,
        JobSearchSession.user_id == current_user.id,
        JobSearchSession.session_status == "running"
    ).first()

    if not session:
        raise HTTPException(status_code=404, detail="Running session not found")

    # Update session status
    session.session_status = "stopped"
    session.completed_at = datetime.utcnow()
    db.commit()

    # Send stop signal to automation
    automation_queue.put({"action": "stop", "session_id": session_id})

    return {"message": "Job search session stopped"}

@app.get("/api/jobs/applications", response_model=List[JobApplicationResponse])
async def get_job_applications(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user's job applications"""
    applications = db.query(JobApplication).filter(
        JobApplication.user_id == current_user.id
    ).offset(skip).limit(limit).all()

    return [
        JobApplicationResponse(
            id=app.id,
            job_title=app.job_title,
            company_name=app.company_name,
            job_location=app.job_location,
            application_status=app.application_status,
            applied_at=app.applied_at,
            job_url=app.job_url
        )
        for app in applications
    ]

@app.get("/api/jobs/applications/stats")
async def get_application_stats(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get application statistics"""
    total_applications = db.query(JobApplication).filter(
        JobApplication.user_id == current_user.id
    ).count()

    successful_applications = db.query(JobApplication).filter(
        JobApplication.user_id == current_user.id,
        JobApplication.application_status == "applied"
    ).count()

    failed_applications = db.query(JobApplication).filter(
        JobApplication.user_id == current_user.id,
        JobApplication.application_status == "failed"
    ).count()

    skipped_applications = db.query(JobApplication).filter(
        JobApplication.user_id == current_user.id,
        JobApplication.application_status == "skipped"
    ).count()

    return {
        "total_applications": total_applications,
        "successful_applications": successful_applications,
        "failed_applications": failed_applications,
        "skipped_applications": skipped_applications,
        "success_rate": (successful_applications / total_applications * 100) if total_applications > 0 else 0
    }

@app.get("/api/jobs/sessions")
async def get_job_sessions(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user's job search sessions"""
    sessions = db.query(JobSearchSession).filter(
        JobSearchSession.user_id == current_user.id
    ).order_by(JobSearchSession.started_at.desc()).all()

    return [
        {
            "id": session.id,
            "status": session.session_status,
            "total_jobs_found": session.total_jobs_found,
            "jobs_applied": session.jobs_applied,
            "jobs_skipped": session.jobs_skipped,
            "started_at": session.started_at,
            "completed_at": session.completed_at,
            "search_parameters": json.loads(session.search_parameters) if session.search_parameters else {}
        }
        for session in sessions
    ]

# WebSocket endpoint for real-time updates
@app.websocket("/ws/jobs/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: int):
    await manager.connect(websocket)
    try:
        while True:
            # Keep connection alive and listen for messages
            data = await websocket.receive_text()
            # Echo back for now (can be extended for bidirectional communication)
            await manager.send_personal_message(f"Message received: {data}", websocket)
    except WebSocketDisconnect:
        manager.disconnect(websocket)

# Background job automation function
async def run_job_automation(session_id: int, search_request: JobSearchRequest, user_id: int):
    """Run the LinkedIn job automation in the background"""
    db = SessionLocal()

    try:
        # Update config with search parameters
        config.keywords = search_request.keywords
        config.location = search_request.locations
        config.experienceLevels = search_request.experience_levels
        config.jobType = search_request.job_types
        config.remote = search_request.remote_options
        config.datePosted = search_request.date_posted

        if search_request.salary_range:
            config.salary = search_request.salary_range

        # Create custom LinkedIn automation class with API integration
        linkedin_bot = LinkedinAPIBot(session_id, user_id, db, manager)

        # Start the automation
        await linkedin_bot.run_automation()

    except Exception as e:
        # Update session status to failed
        session = db.query(JobSearchSession).filter(JobSearchSession.id == session_id).first()
        if session:
            session.session_status = "failed"
            session.completed_at = datetime.utcnow()
            db.commit()

        # Broadcast error to WebSocket
        await manager.broadcast(json.dumps({
            "type": "error",
            "session_id": session_id,
            "message": f"Automation failed: {str(e)}"
        }))

    finally:
        db.close()

class LinkedinAPIBot:
    """Extended LinkedIn bot with API integration"""

    def __init__(self, session_id: int, user_id: int, db: Session, connection_manager: ConnectionManager):
        self.session_id = session_id
        self.user_id = user_id
        self.db = db
        self.manager = connection_manager
        self.linkedin_bot = None
        self.should_stop = False

    async def run_automation(self):
        """Run the LinkedIn automation with real-time updates"""
        try:
            # Initialize LinkedIn bot
            self.linkedin_bot = Linkedin()

            # Override the displayWriteResults method to send updates via WebSocket
            original_display_method = self.linkedin_bot.displayWriteResults
            self.linkedin_bot.displayWriteResults = self.custom_display_results

            # Start monitoring for stop signals
            stop_thread = threading.Thread(target=self.monitor_stop_signals)
            stop_thread.daemon = True
            stop_thread.start()

            # Run the job application process
            await self.run_job_application_process()

        except Exception as e:
            await self.send_update("error", f"Automation error: {str(e)}")
            raise

    def monitor_stop_signals(self):
        """Monitor for stop signals from the API"""
        while not self.should_stop:
            try:
                if not automation_queue.empty():
                    message = automation_queue.get_nowait()
                    if message.get("action") == "stop" and message.get("session_id") == self.session_id:
                        self.should_stop = True
                        break
            except queue.Empty:
                pass
            time.sleep(1)

    async def run_job_application_process(self):
        """Modified job application process with database integration"""
        # Generate URLs
        self.linkedin_bot.generateUrls()

        count_applied = 0
        count_jobs = 0

        url_data = utils.getUrlDataFile()

        for url in url_data:
            if self.should_stop:
                break

            # Process each URL and job
            # This is a simplified version - you'll need to adapt the full logic
            await self.send_update("progress", f"Processing URL: {url}")

            # Update session statistics
            session = self.db.query(JobSearchSession).filter(JobSearchSession.id == self.session_id).first()
            if session:
                session.total_jobs_found = count_jobs
                session.jobs_applied = count_applied
                self.db.commit()

        # Mark session as completed
        session = self.db.query(JobSearchSession).filter(JobSearchSession.id == self.session_id).first()
        if session:
            session.session_status = "completed"
            session.completed_at = datetime.utcnow()
            self.db.commit()

        await self.send_update("completed", f"Job search completed. Applied to {count_applied} jobs out of {count_jobs}")

    async def custom_display_results(self, line_to_write: str):
        """Custom method to handle job application results"""
        # Parse the result line and save to database
        if "Just Applied to this job:" in line_to_write:
            # Extract job information and save to database
            job_url = line_to_write.split("Just Applied to this job: ")[-1]

            # Create job application record
            job_app = JobApplication(
                user_id=self.user_id,
                job_id=job_url.split("/")[-1] if "/" in job_url else job_url,
                job_title="Job Title",  # Extract from line_to_write
                company_name="Company Name",  # Extract from line_to_write
                job_location="Location",  # Extract from line_to_write
                job_url=job_url,
                application_status="applied"
            )
            self.db.add(job_app)
            self.db.commit()

        # Send real-time update via WebSocket
        await self.send_update("job_update", line_to_write)

    async def send_update(self, update_type: str, message: str):
        """Send real-time update via WebSocket"""
        update_data = {
            "type": update_type,
            "session_id": self.session_id,
            "message": message,
            "timestamp": datetime.utcnow().isoformat()
        }

        await self.manager.broadcast(json.dumps(update_data))

# Health check endpoint
@app.get("/api/health")
async def health_check():
    return {"status": "healthy", "timestamp": datetime.utcnow()}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
