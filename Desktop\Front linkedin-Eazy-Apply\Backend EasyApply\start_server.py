#!/usr/bin/env python3
"""
Startup script for LinkedIn EasyApply API Server
"""

import os
import sys
import subprocess
import uvicorn
from pathlib import Path

def install_requirements():
    """Install required packages"""
    print("📦 Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Requirements installed successfully!")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install requirements: {e}")
        sys.exit(1)

def check_config():
    """Check if config is properly set up"""
    print("🔧 Checking configuration...")
    
    try:
        import config
        
        # Check LinkedIn credentials
        if not config.email or not config.password:
            print("❌ LinkedIn credentials not set in config.py")
            print("Please edit config.py and add your LinkedIn email and password")
            return False
        
        if config.email == "<EMAIL>":
            print("⚠️  Warning: Using default email in config.py")
            print("Please update config.py with your LinkedIn credentials")
        
        print("✅ Configuration looks good!")
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import config: {e}")
        return False

def start_server():
    """Start the FastAPI server"""
    print("🚀 Starting LinkedIn EasyApply API Server...")
    print("📡 Server will be available at: http://localhost:8000")
    print("📚 API Documentation: http://localhost:8000/docs")
    print("🔄 WebSocket endpoint: ws://localhost:8000/ws/jobs/{user_id}")
    print("\n" + "="*50)
    
    try:
        uvicorn.run(
            "api_server:app",
            host="0.0.0.0",
            port=8000,
            reload=True,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Server error: {e}")

def main():
    """Main startup function"""
    print("🤖 LinkedIn EasyApply API Server Startup")
    print("="*40)
    
    # Check if we're in the right directory
    if not os.path.exists("api_server.py"):
        print("❌ api_server.py not found. Please run this script from the Backend EasyApply directory")
        sys.exit(1)
    
    # Install requirements
    install_requirements()
    
    # Check configuration
    if not check_config():
        sys.exit(1)
    
    # Start server
    start_server()

if __name__ == "__main__":
    main()
